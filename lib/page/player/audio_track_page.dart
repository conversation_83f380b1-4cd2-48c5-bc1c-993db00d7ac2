import 'package:flutter/material.dart';

class AudioTrackPage extends StatefulWidget {
  const AudioTrackPage({super.key});

  @override
  State<AudioTrackPage> createState() => _AudioTrackPageState();
}

class _AudioTrackPageState extends State<AudioTrackPage> {
  final List<String> _audioTracks = ['音轨 1', '音轨 2', 'English'];
  String _selectedTrack = '音轨 1';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('选择音轨')),
      body: ListView.builder(
        itemCount: _audioTracks.length,
        itemBuilder: (context, index) {
          final track = _audioTracks[index];
          return RadioListTile<String>(
            title: Text(track),
            value: track,
            groupValue: _selectedTrack,
            onChanged: (String? value) {
              if (value != null) {
                setState(() {
                  _selectedTrack = value;
                });
              }
            },
          );
        },
      ),
    );
  }
}
