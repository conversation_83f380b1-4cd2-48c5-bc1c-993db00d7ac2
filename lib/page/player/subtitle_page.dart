import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

class SubtitlePage extends StatefulWidget {
  const SubtitlePage({super.key});

  @override
  State<SubtitlePage> createState() => _SubtitlePageState();
}

class _SubtitlePageState extends State<SubtitlePage> {
  final List<String> _subtitles = ['字幕 1', '字幕 2', 'English'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('字幕选择')),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: _subtitles.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(_subtitles[index]),
                  onTap: () {
                    // TODO: 实现选择字幕的逻辑
                  },
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              onPressed: _pickSubtitle,
              child: const Text('导入外部字幕'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickSubtitle() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['srt', 'ass'],
      );

      if (result != null) {
        // TODO: 处理选择的文件
        final filePath = result.files.single.path;
        if (filePath != null) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('选择了文件: $filePath')));
        }
      } else {
        // 用户取消了文件选择
      }
    } catch (e) {
      // 处理错误
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('选择文件时出错: $e')));
    }
  }
}
